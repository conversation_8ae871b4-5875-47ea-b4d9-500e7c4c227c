task :delete_orphans_by_tenant_id, [:id] => :environment do |task, args|
  id = ENV['id'].to_i
  p id
  Email.where(tenant_id: id).find_in_batches.each do |emails|
    emails.each do |email|
      l_ids = email.email_look_ups.pluck(:look_up_id)
      look_ups = LookUp.where(id: l_ids)
      count = look_ups.where("entity like 'lead%' or entity like 'contact%'").count
      if count.zero? && Email.find_by(id: email.id)
        p "EMAIL ID #{email.id}"
        p email.email_thread.emails.count
        email.email_thread.emails.count.eql? 1 ? email.email_thread.destroy : email.destroy
      end
    end
  end

  LookUp.where(tenant_id: id).find_in_batches.each do |look_ups|
    look_ups.each do |look_up|
      if look_up.email_look_ups.count.zero? 
        p "Deleting #{look_up.as_json}"
        look_up.destroy
      end
    end
  end
end
