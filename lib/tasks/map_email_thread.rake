task :map_email_thread, [:id] => :environment do |task, args|
  id = ENV['id'].to_i
  p id
  Email.joins(:email_thread).where(tenant_id: id).
    where("emails.tenant_id != email_threads.tenant_id").find_in_batches.each do |emails|  
    emails.each do |email|
      tenant_id = email.tenant_id
      look_up_ids = email.email_look_ups.joins(:look_up).where("look_ups.tenant_id != #{tenant_id}").pluck("look_ups.id")
      p "Email id: #{email.id} LookUp id #{look_up_ids}"
      email.email_look_ups.where(look_up_id: look_up_ids).destroy_all
      thread = EmailThread.create(tenant_id: email.tenant_id, owner_id: email.owner_id)
      email.update(email_thread_id: thread.id)
    end
  end
end
