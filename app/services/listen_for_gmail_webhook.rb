class ListenForGmailWebhook
  prepend SimpleCommand
  include ActiveModel::Valida<PERSON>

  def call
    RabbitmqConnection.subscribe(EMAIL_EXCHANGE, GMAIL_WEBHOOK_EVENT, GMAIL_WEBHOOK_QUEUE) do |payload|
      payload = JSON(payload)
      data = payload["params"]["data"]
      decoded_data = Base64.decode64(data)
      Rails.logger.info "Message Received: #{EMAIL_EXCHANGE} | #{GMAIL_WEBHOOK_EVENT} | #{payload} | #{decoded_data} | #{Time.now}"
      command = FetchGmailHistory.call(payload["params"])
      Rails.logger.info "Fetched history: #{payload} | #{decoded_data} | #{Time.now}"
      return unless command.success?
      result = command.result
      FetchGmailMessages.call(result[0], result[1], result[2]) if result.present?
      Rails.logger.info "Fetched messages: #{payload} | #{decoded_data} | #{Time.now}"
    end
  end
end
