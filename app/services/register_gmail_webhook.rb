require 'google/apis/gmail_v1'
class RegisterGmailWebhook
  prepend SimpleCommand

  def initialize(connected_account)
    @connected_account = connected_account
  end

  def call
    gmail = Google::Apis::GmailV1::GmailService.new
    command = GetConnectedAccountAccessToken.call(@connected_account, skip_auth: true)
    if command.success?
      begin
        ENV['GMAIL_SUBSCRIPTION_TOPIC'].split(' ').each do |topic|
          begin
            gmail.authorization = Signet::OAuth2::Client.new(access_token: command.result)
            watch_request = Google::Apis::GmailV1::WatchRequest.new
            watch_request.topic_name = topic
            res = gmail.watch_user('me',  watch_request)
            @connected_account.update_attributes(last_history_id: res.history_id) if @connected_account.last_history_id.blank?
            Rails.logger.info "Successfully watched for #{@connected_account.email}"
          rescue Google::Apis::ServerError, Google::Apis::ClientError => e
            Rails.logger.error "Exception while registering for webhook #{@connected_account.email}: #{e.to_s} | topic: #{topic}"
          end
        end
      rescue Google::Apis::ServerError, Google::Apis::ClientError => e
        Rails.logger.error "Exception while registering for webhook #{@connected_account.email}: #{e.to_s}"
        if e.to_s.include?('Only one user push notification client allowed per developer')
          last_acct = ConnectedAccount.where(email: @connected_account.email).where.not(last_history_id: nil).last
          @connected_account.update(last_history_id: last_acct.last_history_id)
        else
          raise ExceptionHandler::ThirdPartyAPIError
        end
      rescue Google::Apis::AuthorizationError => e
        Rails.logger.error "Exception while registering for webhook #{@connected_account.email} #{e.to_s}"
        raise(ExceptionHandler::ThirdPartyAPIAuthError, ErrorCode.unauthorized)
      end
    end
  end
end
