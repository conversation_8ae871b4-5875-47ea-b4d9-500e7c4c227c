# frozen_string_literal: true

class BounceProcessor
  prepend SimpleCommand

  def initialize(message, connected_account, options = {})
    @message = message
    @options = options
    @connected_account = connected_account
  end

  def call
    bounce_info = detect_bounce
    return unless bounce_info.dig(:is_bounced)

    process_bounce(bounce_info)
    bounce_info
  end

  private

  def detect_bounce
    case @connected_account.provider_name
    when GOOGLE_PROVIDER
      return GmailBounceDetector.call(@message)
    when MICROSOFT_PROVIDER
      return OutlookBounceDetector.call(@message, @options)
    when CUSTOM_PROVIDER
      return SMTPBounceProcessor.call(@message)
    end
  end

  def process_bounce(bounce_info)
    original_email = find_original_email(bounce_info)

    if original_email.present?
      if original_email.status.eql?(Email.statuses['failed'])
        Rails.logger.info "EMAIL_BOUNCE: orignal email status is already failed email id #{original_email.id}"
        return
      end

      old_serialized_email_data = EmailDetailsSerializer.call(original_email, false, nil, false, add_owner_id: true).result
      old_serialized_email_data_for_updated_event = EmailDetailsSerializer.call(original_email, false, nil, true, add_owner_id: true).result

      original_email.update!(
        bounce_type: bounce_info[:bounce_type],
        failed_reason: bounce_info[:failed_reason],
        status: Email.statuses['failed']
      )

      Rails.logger.info "EMAIL_BOUNCE: processed: Original email #{original_email.id} marked as #{bounce_info} #{@connected_account.provider_name == CUSTOM_PROVIDER ? @message : ''} bounce"

      PublishEvent.call(Event::EmailFailed.new(original_email.reload, old_serialized_email_data))
      PublishEvent.call(Event::EmailUpdatedV2.new(original_email, old_serialized_email_data_for_updated_event))

      Rails.logger.info "EMAIL_BOUNCE: Event::EmailFailed published for email id #{original_email.id}"
    else
      Rails.logger.warn "EMAIL_BOUNCE: Could not find original email #{bounce_info}"
    end
  end

  def find_original_email(bounce_info)
    message_id = bounce_info[:original_message_id]
    return nil unless message_id

    Email.where(connected_account: @connected_account)
         .where(
           "global_message_id = ?",
           message_id
         )
         .where(direction: Email.directions[:sent])
         .first
  end
end
