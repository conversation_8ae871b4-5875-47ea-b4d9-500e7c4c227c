class OutlookBounceDetector
  include GraphApi<PERSON>lient

  # Outlook-specific bounce subject patterns
  BOUNCE_SUBJECTS_FOR_MICROSOFT_EXCHANGE = [
    /undeliverable/i
  ].freeze

  EXCLUDED_SUBJECTS_FOR_MICROSOFT_EXCHANGE = [
    /^delivered/i,
    /^relayed/i,
    /^Your mailbox/i,
    /^Your archive mailbox/i
  ].freeze

  BOUNCE_SUBJECTS_FOR_POSTMASTER = [
    /undeliverable/i,
    /^Your message couldn't be delivered/i
  ].freeze

  EXCLUDED_SUBJECTS_FOR_POSTMASTER = [
    /^delivered/i,
    /^relayed/i,
    /^Your mailbox/i,
    /^Your archive mailbox/i
  ].freeze

  # Hard bounce indicators (permanent failures)
  HARD_BOUNCE_PATTERNS = [
    /5\.\d+\.\d+/,  # 5.x.x SMTP codes
    /550/,          # Permanent failure
    /551/,          # User not local
    /553/,          # Mailbox name not allowed
    /554/,          # Transaction failed
    /delivery has failed/i,
    /the following recipients? cannot be reached/i,
    /no such user/i,
    /mailbox (unavailable|not found)/i,
    /address rejected/i,
    /unrecognized.*recipient/i,
    /recipient.*address.*rejected/i,
    /user unknown/i,
    /no such user/i,
    /invalid recipient/i,
    /does not exist/i
  ].freeze

  # Soft bounce indicators (temporary failures)
  SOFT_BOUNCE_PATTERNS = [
    /4\.\d+\.\d+/,  # 4.x.x SMTP codes
    /421/,          # Service not available
    /422/,          # Recipient mailbox full
    /450/,          # Requested action not taken
    /451/,          # Requested action aborted
    /452/,          # Insufficient system storage
    /552/,          # Exceeded storage allocation
    /mailbox.*full/i,
    /quota.*exceeded/i,
    /temporarily.*unavailable/i,
    /try.*again.*later/i,
    /deferred/i,
    /temporary.*failure/i,
    /message.*delayed/i,
    /delivery.*delayed/i
  ].freeze

  MICROSOFT_EXCHANGE_SENDER = /microsoft.*exchange/i;
  POSTMASTER_SENDER = /postmaster/i;

  def initialize(message_data, options)
    @token = options[:token]
    @message = message_data
    @subject = extract_subject
    @body = extract_body
    @from = extract_from
    @headers = {}
  end

  def self.call(message_data, options)
    new(message_data, options).detect_bounce
  end

  def detect_bounce
    Rails.logger.info "EMAIL_BOUNCE: OutlookBounceDetector: Starting Bounce detection"
    Rails.logger.info "EMAIL_BOUNCE: OutlookBounceDetector: bounce_by_sender and subject? #{bounce_by_sender_and_subject?} bounce_by_content? #{bounce_by_content?}"

    return { is_bounced: false } unless is_bounced?

    return { is_bounced: false } unless verify_bounce_with_raw_mime?

    {
      is_bounced: true,
      bounce_type: determine_bounce_type,
      failed_reason: extract_failed_reason,
      original_message_id: @headers['In-Reply-To'],
      current_message_id: @message['internetMessageId']
    }
  end

  private

  def is_bounced?
    bounce_by_sender_and_subject? || bounce_by_content?
  end

  def bounce_by_sender_and_subject?
    return false unless @from

    case true
    when @from.match?(MICROSOFT_EXCHANGE_SENDER)
      return !EXCLUDED_SUBJECTS_FOR_MICROSOFT_EXCHANGE.any? { |pattern| @subject.match?(pattern) }
    when @from.match?(POSTMASTER_SENDER)
      return !EXCLUDED_SUBJECTS_FOR_POSTMASTER.any? { |pattern| @subject.match?(pattern) }
    end
  end

  def bounce_by_content?
    return false unless @body

    content = @body.downcase

    (HARD_BOUNCE_PATTERNS + SOFT_BOUNCE_PATTERNS).any? { |pattern| content.match?(pattern) }
  end

  def bounce_by_dsn_body?
    return false unless @dsn_body

    content = @dsn_body.downcase

    (HARD_BOUNCE_PATTERNS + SOFT_BOUNCE_PATTERNS).any? { |pattern| content.match?(pattern) }
  end

  def verify_bounce_with_raw_mime?
    update_body_and_headers_from_mime_message(get_raw_mime_message(@message['id']))
    
    bounce_by_dsn_body?
  end

  def determine_bounce_type
    if @dsn_body
      content = @dsn_body.downcase

      return 'hard' if HARD_BOUNCE_PATTERNS.any? { |pattern| content.match?(pattern) }
      return 'soft' if SOFT_BOUNCE_PATTERNS.any? { |pattern| content.match?(pattern) }
    end

    if @body
      content = @body.downcase

      return 'hard' if HARD_BOUNCE_PATTERNS.any? { |pattern| content.match?(pattern) }
      return 'soft' if SOFT_BOUNCE_PATTERNS.any? { |pattern| content.match?(pattern) }
    end

    'hard'
  end

  def extract_failed_reason
    if @dsn_body
      diagnostic_code = nil
      status = nil

      @dsn_body.each_line do |line|
        case line
        when /^Diagnostic-Code:\s*(.+)$/i
          diagnostic_code ||= $1.strip   # prefer first Diagnostic-Code
        when /^Status:\s*(.+)$/i
          status ||= "Status: #{$1.strip}"
        end
      end

      return diagnostic_code if diagnostic_code
      return status if status
    end

    if @body
      @body.each_line do |line|
        return line.strip if HARD_BOUNCE_PATTERNS.any? { |regex| line.match?(regex) }
        return line.strip if SOFT_BOUNCE_PATTERNS.any? { |regex| line.match?(regex) }
      end
    end

    nil
  end

  def extract_body
    return '' unless @message.is_a?(Hash)
    @message.dig('body', 'content') || ''
  end

  def extract_from
    return nil unless @message.is_a?(Hash)
    @message.dig('from', 'emailAddress', 'address')
  end

  def extract_subject
    return '' unless @message.is_a?(Hash)
    @message['subject'] || ''
  end

  def update_body_and_headers_from_mime_message(raw_mime)
    parser = RMail::Parser.new
    message = parser.parse(raw_mime)

    message.header.send(:fields).each do |field|
      @headers[field.name] = field.value
    end

    if message.multipart?
      message.each_part do |part|
        if part.header['Content-Type']&.include?("message/delivery-status")
          @dsn_body = part.body
        end
      end
    end
  end
end
